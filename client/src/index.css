/* ---------- Google Fonts Imports (Must be first) ---------- */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Jersey+10:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=VT323:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P:wght@400&display=swap');

/* ---------- Font Face ---------- */
@font-face {
  font-family: 'Metamorphous'; /* Capital M is important! */
  src: url('/fonts/metamorphous-latin-400-normal.woff2') format('woff2'),
       url('/fonts/metamorphous-latin-400-normal.woff') format('woff');
  font-weight: 25;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alagard';
  src: url('/fonts/alagard.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* ---------- Tailwind First ---------- */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ---------- Reset & Globals ---------- */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Force Alagard font globally - but preserve layout behavior */
* {
  font-family: 'Alagard', 'Jersey 10', sans-serif !important;
  box-sizing: border-box;
}

html, body {
  min-height: 100dvh;
  margin: 0;
  padding: 0; /* Ensure no padding on body */
  background-color: #0b1f2a;
  color: #e0f4ff;
  font-family: 'Alagard', 'Jersey 10', sans-serif;
}

#root, .App {
  min-height: 100dvh;
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

.App {
  flex: 1 1 0%;
  min-height: 0;
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

/* ---------- Gothic Nordic Runic Theme Colors ---------- */
:root {
  --theme-accent: #3fcaff;
  --theme-dark: #0b1f2a;
  --theme-darker: #000814;
  --textbox-color: #1e293b;
  --font-code: 'VT323', 'source-code-pro', Menlo, Monaco, Consolas, 'Courier New', monospace;
  --font-title: 'Metamorphous', cursive;
  --font-ui: 'Alagard', 'Jersey 10', monospace;
  --font-secondary: 'Alagard', 'Jersey 10', cursive;
  --font-main: 'Alagard', 'Jersey 10', sans-serif;
  --font-retro: 'Press Start 2P', cursive;
  
  /* Nordic Runic Palette - Inspired by MartyMcGlovin's UI */
  --game-primary: #4A9EFF;     /* Runic Ice Blue */
  --game-secondary: #00D4FF;   /* Frost Cyan */
  --game-accent: #87CEEB;      /* Mystical Blue (updated from gold) */
  --game-dark: #1A2332;        /* Nordic Stone */
  --game-darker: #0F1419;      /* Void Black */
  --game-tertiary: #87CEEB;    /* Mystical Blue */
  
  /* Class Colors - Nordic Theme */
  --tank-color: #5DADE2;       /* Glacier Blue */
  --dps-color: #FF6B35;        /* Forge Fire */
  --healer-color: #2ECC71;     /* Forest Green */
  
  /* Element Colors - Runic Theme */
  --health-color: #E74C3C;     /* Blood Red */
  --mana-color: #3498DB;       /* Runic Blue */
  --xp-color: #9B59B6;         /* Soul Purple */
  --gold-color: #87CEEB;       /* Mystical Blue (updated from gold) */
  
  /* Nordic Runic Effects - MUTED FOR PERFORMANCE */
  --shadow-glow: 0 0 8px rgba(74, 158, 255, 0.15);
  --border-glow: 0 0 6px rgba(74, 158, 255, 0.12);
  --text-glow: 0 0 4px rgba(0, 212, 255, 0.25);
  --runic-glow: 0 0 8px rgba(135, 206, 235, 0.2);
  
  /* Pixel Art Sizing */
  --pixel-border: 2px;
  --gothic-border: 3px;
  --corner-radius: 8px;
  --panel-radius: 12px;
}

/* Dark Mode Theme Updates */
.dark-mode {
  --theme-accent: #00d4ff;
  --theme-dark: #0f1419;
  --theme-darker: #0a0e12;
  --game-primary: #00d4ff;
  --game-secondary: #4a9eff;
  --shadow-glow: 0 0 8px rgba(0, 212, 255, 0.15);
  --border-glow: 0 0 6px rgba(0, 212, 255, 0.12);
  --text-glow: 0 0 4px rgba(74, 158, 255, 0.25);
}

/* ---------- Typography ---------- */
body {
  font-family: var(--font-code);
  font-size: 14px;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  image-rendering: pixelated; /* For pixel art assets */
  /* Enhanced text rendering for better readability */
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga", "kern";
}

h1, h2, h3 {
  font-family: var(--font-title);
  letter-spacing: 1px;
  color: var(--theme-accent);
  text-shadow: var(--text-glow);
  /* Enhanced heading readability */
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga", "kern";
}

.font-gothic {
  font-family: var(--font-title);
  /* Improved readability for gothic font with runic theme */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 8px rgba(74, 158, 255, 0.3);
}

.font-metamorphous {
  font-family: 'Metamorphous', var(--font-title);
  /* Enhanced contrast for Metamorphous with runic theme */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9), 0 0 10px rgba(74, 158, 255, 0.4);
}

.font-ui {
  font-family: var(--font-ui);
  /* Improved Jersey 10 readability with runic theme */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7), 0 0 6px rgba(0, 212, 255, 0.3);
  letter-spacing: 0.5px;
}

.font-secondary {
  font-family: var(--font-secondary);
  /* Enhanced secondary font readability */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.6);
  letter-spacing: 0.3px;
}

.font-retro {
  font-family: var(--font-retro);
  font-size: 1.4em;
  /* Pixel font optimization */
  text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.8);
  letter-spacing: 1px;
}

.font-logs {
  font-family: var(--font-code) !important;
  font-size: 1.1rem !important;
  line-height: 1.5 !important;
  /* Enhanced log readability with VT323 monospace - matches chat font */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
  background: rgba(0, 0, 0, 0.3) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  letter-spacing: 0.5px !important;
}

.font-equipment {
  font-family: var(--font-secondary) !important;
  font-size: 1.4rem !important;
  /* Better equipment text contrast */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.7) !important;
}

.font-stats {
  font-family: var(--font-secondary) !important;
  font-size: 1.5rem !important;
  /* Enhanced stats readability with runic theme */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 8px rgba(74, 158, 255, 0.4) !important;
  font-weight: 700 !important;
}

.font-chat {
  font-family: var(--font-code) !important;
  font-size: 1.3rem !important;
  line-height: 1.5 !important;
  /* Chat text enhancement - VT323 is much more readable for chat */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8) !important;
  background: rgba(0, 0, 0, 0.3) !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  letter-spacing: 0.5px !important;
}

.font-panel-title {
  font-family: var(--font-secondary) !important;
  font-size: 1.8rem !important;
  font-weight: 600 !important;
  /* Panel title enhancement with runic theme */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9), 0 0 12px rgba(74, 158, 255, 0.5) !important;
  letter-spacing: 1px !important;
}

/* Menu content font sizes - match Adventure Log readability */
.font-menu-content {
  font-family: var(--font-ui) !important;
  font-size: 1.3rem !important;
  line-height: 1.5 !important;
  /* Menu content readability improvement */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7) !important;
  background: rgba(0, 0, 0, 0.2) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
}

.font-menu-small {
  font-family: var(--font-ui) !important;
  font-size: 1.2rem !important;
  line-height: 1.4 !important;
  /* Small menu text enhancement */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.6) !important;
}

/* Character Sheet Specific Sizes - Following UI/UX Doc */
.class-territory-large {
  font-size: 24px !important;
  line-height: 26px !important;
  font-family: var(--font-secondary) !important;
  font-weight: 600 !important;
}

.level-badge-large {
  font-size: 24px !important;
  font-family: var(--font-secondary) !important;
  padding: 8px 12px !important;
}

/* Level indicator for left side of character name */
.level-indicator-large {
  font-size: 2.5rem !important;
  font-family: var(--font-main) !important;
  font-weight: 900 !important;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8), 0 0 20px rgba(74, 158, 255, 0.4) !important;
}

/* Extra specificity for level indicator to override any App.css rules */
span.level-indicator-large,
.level-indicator-large span,
p .level-indicator-large {
  font-family: var(--font-main) !important;
  font-size: 2.5rem !important;
  font-weight: 900 !important;
}

/* Gold indicator to match health/mana styling */
.gold-indicator-large {
  font-size: 1.5rem !important;
  font-family: var(--font-secondary) !important;
  font-weight: 600 !important;
  color: #87ceeb !important;
}

/* Health/Mana/Experience Label Overrides */
.health-mana-xp-labels {
  font-size: 1.5rem !important;
  font-family: var(--font-secondary) !important;
  font-weight: 600 !important;
}

/* Gold text override */
.text-element-gold {
  color: #87ceeb !important;
}

/* CharacterStats specific overrides to prevent App.css interference */
.character-stats-container .text-xl,
.character-stats-container span.text-xl,
.character-stats-container p.text-xl {
  font-size: 1.25rem !important;
  font-family: var(--font-secondary) !important;
}

.character-stats-container .text-3xl,
.character-stats-container span.text-3xl,
.character-stats-container p.text-3xl {
  font-size: 1.875rem !important;
  font-family: var(--font-secondary) !important;
}

.character-stats-container .text-lg,
.character-stats-container span.text-lg,
.character-stats-container p.text-lg,
.character-stats-container div.text-lg {
  font-size: 1.125rem !important;
  font-family: var(--font-secondary) !important;
}

/* 🚀 NEW: Character Stats Breakdown Spacing Fix */
.character-stats-container .flex.justify-between.items-center {
  padding: 0.375rem 1rem !important;
  margin: 0.125rem 0 !important;
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 6px !important;
  border: 1px solid rgba(74, 158, 255, 0.1) !important;
}

.character-stats-container .flex.justify-between.items-center span {
  background: none !important;
  padding: 0 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Override global p, li styles for character stats breakdown */
.character-stats-container .flex.justify-between.items-center span,
.character-stats-container div[class*="mx-"] span,
.character-stats-container div[class*="px-"] span {
  background: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: var(--font-secondary) !important;
  letter-spacing: 0.5px !important;
}

/* Primary action buttons should use Jersey 10 - Override btn-gothic with highest specificity */
button.btn-gothic.primary-action-btn,
.btn-gothic.primary-action-btn,
.primary-action-btn.btn-gothic,
button.primary-action-btn,
.primary-action-btn {
  font-family: var(--font-main) !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
}

.btn-gothic {
  background: linear-gradient(135deg, 
    var(--game-dark) 0%, 
    var(--game-primary) 30%,
    var(--game-secondary) 70%,
    var(--game-dark) 100%
  );
  border: var(--pixel-border) solid var(--game-primary);
  color: white;
  font-family: var(--font-ui);
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.55rem 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8), var(--text-glow);
  box-shadow: 
    var(--border-glow),
    inset 0 1px 2px rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  min-height: 2.25rem;
  /* Enhanced button text readability with runic theme */
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.9), 
    0 0 8px rgba(0, 212, 255, 0.4),
    0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.btn-gothic:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 0 20px rgba(74, 158, 255, 0.6),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
  border-color: var(--game-secondary);
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.9), 
    0 0 12px rgba(0, 212, 255, 0.8),
    0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.btn-gothic:active {
  transform: translateY(0);
}

/* Enhanced paragraph and text readability with runic theme */
p, li {
  font-family: var(--font-code);
  letter-spacing: 0.5px;
  color: #e0f4ff; /* Updated to frost blue */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.15);
  padding: 1px 2px;
  border-radius: 2px;
  line-height: 1.5;
}

/* Enhanced Readability Utility Classes */
.text-high-contrast {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9), 0 0 8px rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-weight: 600 !important;
}

.text-readable {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
  background: rgba(0, 0, 0, 0.25) !important;
  padding: 1px 3px !important;
  border-radius: 2px !important;
}

.text-ultra-readable {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.95) !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)) !important;
  padding: 3px 8px !important;
  border-radius: 6px !important;
  font-weight: 700 !important;
  border: 1px solid rgba(74, 158, 255, 0.3) !important;
}

/* ---------- Runic Nordic Game Elements ---------- */
.gothic-panel {
  background: linear-gradient(135deg, 
    rgba(15, 20, 25, 0.95) 0%, 
    rgba(26, 35, 50, 0.90) 50%, 
    rgba(15, 20, 25, 0.95) 100%
  );
  border: var(--gothic-border) solid;
  border-image: linear-gradient(45deg, 
    var(--game-primary), 
    var(--game-secondary), 
    var(--game-tertiary),
    var(--game-primary)
  ) 1;
  border-radius: var(--panel-radius);
  box-shadow: 
    var(--shadow-glow),
    inset 0 1px 3px rgba(74, 158, 255, 0.15),
    0 0 30px rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(10px);
  max-width: 690px; /* Increased by ~15% (600px * 1.15) */
  margin: 0 auto; /* Center the panel */
}

.pixel-border {
  border: var(--pixel-border) solid var(--game-primary);
  border-radius: var(--corner-radius);
  box-shadow: var(--border-glow);
}

.runic-border {
  border: var(--gothic-border) solid var(--game-primary);
  border-radius: var(--corner-radius);
  box-shadow: var(--runic-glow);
  position: relative;
}

.runic-border::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    transparent 0%, 
    rgba(74, 158, 255, 0.3) 25%, 
    transparent 50%, 
    rgba(0, 212, 255, 0.3) 75%, 
    transparent 100%
  );
  border-radius: inherit;
  z-index: -1;
  animation: runic-shimmer 4s ease-in-out infinite;
}

.health-bar-gothic {
  background: linear-gradient(90deg, 
    #C0392B 0%, 
    #E74C3C 50%, 
    #EC7063 100%
  );
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    0 0 8px rgba(231, 76, 60, 0.4);
}

.mana-bar-gothic {
  background: linear-gradient(90deg, 
    #2E86C1 0%, 
    #3498DB 50%, 
    #5DADE2 100%
  );
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    0 0 12px rgba(52, 152, 219, 0.5);
}

.xp-bar-gothic {
  background: linear-gradient(90deg, 
    #7D3C98 0%, 
    #9B59B6 50%, 
    #BB8FCE 100%
  );
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(155, 89, 182, 0.4);
}

.travel-bar-gothic {
  background: linear-gradient(90deg, 
    #2E86C1 0%, 
    #4a9eff 35%,
    #00d4ff 65%,
    #87ceeb 100%
  );
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    0 0 12px rgba(74, 158, 255, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ---------- Inputs ---------- */
input, textarea {
  font-family: var(--font-code);
  font-weight: 600;
  background: linear-gradient(135deg, 
    rgba(15, 20, 25, 0.8) 0%, 
    rgba(26, 35, 50, 0.9) 100%
  );
  color: #e0f4ff;
  padding: 0.75rem 1rem;
  border: var(--pixel-border) solid var(--game-primary);
  border-radius: var(--corner-radius);
  box-shadow: 
    var(--border-glow),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Login form button overrides */
.login-form button {
  font-family: var(--font-secondary) !important;
}

.login-form button.btn-gothic.primary-action-btn {
  font-family: var(--font-main) !important;
  font-size: 1.375rem !important; /* text-xl */
  padding: 0.75rem 1.5rem !important;
  background: linear-gradient(135deg, var(--game-dark) 0%, var(--game-primary) 30%, var(--game-secondary) 70%, var(--game-dark) 100%) !important;
  border-color: var(--game-primary) !important;
  transform: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.login-form button[type="button"] {
  font-family: var(--font-secondary) !important;
  font-size: 1.25rem !important; /* text-xl */
  padding: 0.75rem 1rem !important;
  background: linear-gradient(135deg, var(--game-dark) 0%, var(--game-primary) 30%, var(--game-secondary) 70%, var(--game-dark) 100%) !important;
  border-color: var(--game-primary) !important;
  color: white !important;
  transform: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* ---------- Login Form Responsive Design ---------- */
/* Basic responsive design - detailed widescreen rules are above */

input:focus, textarea:focus {
  outline: none;
  border-color: var(--game-secondary);
  box-shadow: 
    0 0 15px rgba(74, 158, 255, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ---------- Nordic Runic Animations ---------- */
@keyframes gothic-pulse {
  0%, 100% { 
    box-shadow: 
      0 0 3px rgba(74, 158, 255, 0.1),
      0 0 6px rgba(74, 158, 255, 0.08);
  }
  50% { 
    box-shadow: 
      0 0 4px rgba(74, 158, 255, 0.15),
      0 0 8px rgba(0, 212, 255, 0.12);
  }
}

@keyframes pixel-flicker {
  0%, 98%, 100% { opacity: 1; }
  99% { opacity: 0.98; }
}

@keyframes runic-shimmer {
  0%, 100% { 
    opacity: 0.6;
    transform: rotate(0deg);
  }
  50% { 
    opacity: 0.8;
    transform: rotate(2deg);
  }
}

@keyframes frost-flow {
  0% { 
    background-position: 0% 50%;
  }
  50% { 
    background-position: 100% 50%;
  }
  100% { 
    background-position: 0% 50%;
  }
}

.animate-gothic-pulse {
  animation: gothic-pulse 8s ease-in-out infinite;
}

.animate-pixel-flicker {
  animation: pixel-flicker 12s ease-in-out infinite;
}

.animate-runic-shimmer {
  animation: runic-shimmer 15s ease-in-out infinite;
}

.animate-frost-flow {
  animation: frost-flow 20s ease-in-out infinite;
  background-size: 200% 200%;
}

/* ---------- Nordic Class-Specific Styling - MUTED ---------- */
.tank-theme {
  --primary: var(--tank-color);
  border-color: var(--tank-color);
  box-shadow: 0 0 6px rgba(93, 173, 226, 0.2);
}

.dps-theme {
  --primary: var(--dps-color);
  border-color: var(--dps-color);
  box-shadow: 0 0 6px rgba(255, 107, 53, 0.2);
}

.healer-theme {
  --primary: var(--healer-color);
  border-color: var(--healer-color);
  box-shadow: 0 0 6px rgba(46, 204, 113, 0.2);
}

/* ---------- Runic Utility Classes - MUTED ---------- */
.text-glow {
  text-shadow: 0 0 4px rgba(0, 212, 255, 0.3);
}

.text-runic-glow {
  text-shadow: 0 0 4px rgba(0, 212, 255, 0.4), 0 0 8px rgba(74, 158, 255, 0.2);
}

.border-glow {
  box-shadow: var(--border-glow);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.runic-glow {
  box-shadow: var(--runic-glow);
}

.frost-gradient {
  background: linear-gradient(45deg, 
    rgba(74, 158, 255, 0.05) 0%,
    rgba(0, 212, 255, 0.08) 25%,
    rgba(135, 206, 235, 0.04) 50%,
    rgba(0, 212, 255, 0.08) 75%,
    rgba(74, 158, 255, 0.05) 100%
  );
}

/* ---------- Custom Panel Colors for Functional Areas - MUTED ---------- */
.panel-blue {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.15) 0%, 
    rgba(29, 78, 216, 0.12) 100%
  ) !important;
  border-color: rgba(59, 130, 246, 0.25) !important;
}

.panel-amber {
  background: linear-gradient(135deg, 
    rgba(245, 158, 11, 0.3) 0%, 
    rgba(217, 119, 6, 0.2) 100%
  ) !important;
  border-color: rgba(245, 158, 11, 0.4) !important;
}

.panel-purple {
  background: linear-gradient(135deg, 
    rgba(147, 51, 234, 0.3) 0%, 
    rgba(126, 34, 206, 0.2) 100%
  ) !important;
  border-color: rgba(147, 51, 234, 0.4) !important;
}

.panel-green {
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.3) 0%, 
    rgba(21, 128, 61, 0.2) 100%
  ) !important;
  border-color: rgba(34, 197, 94, 0.4) !important;
}

.panel-yellow {
  background: linear-gradient(135deg, 
    rgba(234, 179, 8, 0.3) 0%, 
    rgba(202, 138, 4, 0.2) 100%
  ) !important;
  border-color: rgba(234, 179, 8, 0.4) !important;
}

.panel-cyan {
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.3) 0%, 
    rgba(14, 116, 144, 0.2) 100%
  ) !important;
  border-color: rgba(6, 182, 212, 0.4) !important;
}

.panel-red {
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.3) 0%, 
    rgba(185, 28, 28, 0.2) 100%
  ) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
}

.panel-orange {
  background: linear-gradient(135deg, 
    rgba(249, 115, 22, 0.3) 0%, 
    rgba(194, 65, 12, 0.2) 100%
  ) !important;
  border-color: rgba(249, 115, 22, 0.4) !important;
}

.panel-dark .bg-gray-800\/50 {
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.8) 0%, 
    rgba(8, 8, 12, 0.7) 50%,
    rgba(0, 0, 0, 0.8) 100%
  ) !important;
}

.panel-dark .bg-gray-700\/50 {
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.6) 0%, 
    rgba(12, 12, 18, 0.5) 50%,
    rgba(0, 0, 0, 0.6) 100%
  ) !important;
  border-color: rgba(60, 60, 80, 0.3) !important;
}

/* ---------- Character Name Override ---------- */
.character-name-massive {
  font-size: 1.875rem !important; /* Half of previous 3.75rem */
  line-height: 1 !important;
  font-weight: bold !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
}

@media (min-width: 640px) {
  .character-name-massive {
    font-size: 3rem !important; /* Half of previous 6rem */
  }
}

/* ---------- Responsive Typography & Layout Improvements ---------- */

/* Base responsive font scaling for better readability across screen sizes */
@media (min-width: 1500px) {
  /* Large screens (1500px+) - Better scaling for desktop/widescreen - WIDE DESKTOP! */
  .font-logs {
    font-size: 1.2rem !important;
  }
  
  .font-equipment {
    font-size: 1.1rem !important;
  }
  
  .font-stats {
    font-size: 1.6rem !important;
  }
  
  .font-chat {
    font-size: 1.2rem !important;
  }
  
  .font-panel-title {
    font-size: 1.8rem !important;
  }
  
  .font-menu-content {
    font-size: 1.2rem !important;
  }
  
  .class-territory-large {
    font-size: 26px !important;
    line-height: 28px !important;
  }
  
  .level-badge-large {
    font-size: 26px !important;
  }
  
  .gold-indicator-large {
    font-size: 1.6rem !important;
  }
  
  .health-mana-xp-labels {
    font-size: 1.6rem !important;
  }
}

@media (min-width: 1440px) {
  /* Extra large screens (1440px+) - Optimal for 1440p and ultrawide */
  .font-logs {
    font-size: 1.3rem !important;
  }
  
  .font-panel-title {
    font-size: 2rem !important;
  }
  
  .font-menu-content {
    font-size: 1.3rem !important;
  }
  
  .class-territory-large {
    font-size: 28px !important;
    line-height: 30px !important;
  }
  
  .level-badge-large {
    font-size: 28px !important;
  }
  
  .primary-action-btn {
    font-size: 1.3rem !important;
  }
}

@media (min-width: 1920px) {
  /* Ultra-wide screens (1920px+) - Maximum readability */
  .font-logs {
    font-size: 1.4rem !important;
  }
  
  .font-panel-title {
    font-size: 2.2rem !important;
  }
  
  .font-menu-content {
    font-size: 1.4rem !important;
  }
  
  .class-territory-large {
    font-size: 30px !important;
    line-height: 32px !important;
  }
  
  .primary-action-btn {
    font-size: 1.4rem !important;
  }
}

/* ---------- Simple Character Card Layout ---------- */
.character-card-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* ---------- Simple Mobile Optimization ---------- */
@media (max-width: 768px) {
  .gothic-panel {
    padding: 1rem !important;
  }
  
  .btn-gothic {
    font-size: 0.9rem !important;
  }
  
  .game-container {
    padding: 0.25rem;
  }
}

/* ---------- Simple Layout System ---------- */
.game-container {
  width: 100%;
  max-width: 1200px;
  min-height: 100vh;
  padding: 0.5rem;
  margin: 0 auto;
  box-sizing: border-box;
}

/* ---------- Rarity Color Overrides ---------- */
/* Ensure rarity colors always take precedence */
.rarity-common {
  color: #6b7280 !important; /* gray-500 - darker gray that clearly indicates common/less desirable */
}

.rarity-uncommon {
  color: #4ade80 !important; /* green-400 */
}

.rarity-rare {
  color: #60a5fa !important; /* blue-400 */
}

.rarity-epic {
  color: #c084fc !important; /* purple-400 */
}

.rarity-legendary {
  color: #FFA500 !important; /* Bright Orange instead of Mystical Blue */
  text-shadow: 0 0 5px rgba(255, 165, 0, 0.5) !important;
}

/* Equipment and inventory item name overrides - REMOVED GLOW FOR READABILITY */
.equipment-item-name,
.inventory-item-name {
  font-weight: 600 !important;
}

/* Zone Boss Raid Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.8);
  }
  100% {
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
  }
}
